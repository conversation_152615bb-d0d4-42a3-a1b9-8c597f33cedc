# Apple Sign-In Web Authentication Troubleshooting Guide

## Overview

This guide helps resolve the `UNKNOWN_SIWA_ERROR` that occurs during Apple Sign-In web authentication. This error typically indicates configuration issues between Apple Developer Portal, Firebase, and your web application.

## Quick Diagnosis

Run the debugging script to identify issues:

```bash
./scripts/debug_apple_signin_web.sh
```

## Common Causes and Solutions

### 1. **Firebase Configuration Mismatch** ✅ FIXED

**Issue**: Inconsistent Firebase configuration between `web/firebase-config.js` and `lib/firebase_options.dart`

**Solution**: Updated `web/firebase-config.js` with correct values:
- ✅ App ID: `1:1068175978703:web:a62aa1bf9668a7b9dcc7ba`
- ✅ Storage Bucket: `bloomg-flutter.firebasestorage.app`
- ✅ Measurement ID: `G-17QGQMB6GZ`

### 2. **Missing Apple Sign-In JavaScript SDK** ✅ FIXED

**Issue**: Apple Sign-In JavaScript SDK not loaded in web application

**Solution**: Added SDK to `web/index.html`:
```html
<script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
```

### 3. **Domain Configuration Mismatch** ✅ FIXED

**Issue**: Service ID `com.algomash.weblogin` was configured for wrong domain

**Root Cause**: App deployed at `bloomg-flutter.web.app` but configured for `bloomg-flutter.firebaseapp.com`

**Solution Applied**:
1. **Updated All Configuration Files**:
   - `public/index.html` - Apple Sign-In meta tags
   - `web/index.html` - Apple Sign-In meta tags
   - `public/firebase-config.js` - Apple Sign-In config object
   - `web/firebase-config.js` - Apple Sign-In config object
   - `lib/auth/repository/firebase_auth_repository.dart` - WebAuthenticationOptions

2. **Apple Developer Portal Configuration Required**:
   - Go to [Apple Developer Portal](https://developer.apple.com/account/resources/identifiers/list/serviceId)
   - Find Service ID: `com.algomash.weblogin`
   - Update domain from `bloomg-flutter.firebaseapp.com` to `bloomg-flutter.web.app`
   - Update redirect URI to: `https://bloomg-flutter.web.app/__/auth/handler`
   - Verify domain ownership (domain association file already exists)

3. **Domain Verification**:
   - Domain association file accessible at: `https://bloomg-flutter.web.app/.well-known/apple-developer-domain-association.txt`
   - Contains Team ID: `598AQBZ36R`
   - Ensure domain verification status shows "Verified" in Apple Developer Portal

### 4. **Firebase Console Apple Provider Configuration** ⚠️ REQUIRES VERIFICATION

**Required Actions**:

1. **Enable Apple Sign-In Provider**:
   - Go to [Firebase Console](https://console.firebase.google.com/project/bloomg-flutter/authentication/providers)
   - Navigate to Authentication > Sign-in method
   - Ensure Apple provider is enabled

2. **Verify Configuration**:
   - Service ID: `com.algomash.weblogin`
   - Team ID: `598AQBZ36R`
   - Key ID: `99PLQH6WMP`
   - Private Key: Ensure valid .p8 file is uploaded

## Testing Steps

### 1. Local Development Testing

```bash
# Start Flutter web development server
flutter run -d chrome --web-port 3000

# Test Apple Sign-In at http://localhost:3000
```

### 2. Browser Console Debugging

1. Open Developer Tools (F12)
2. Go to Console tab
3. Attempt Apple Sign-In
4. Look for specific error messages:
   - `invalid_client`: Service ID configuration issue
   - `invalid_request`: Missing or incorrect parameters
   - `unauthorized_client`: Domain not verified

### 3. Network Request Analysis

Monitor network requests during Apple Sign-In:
1. Open Developer Tools > Network tab
2. Filter by "appleid.apple.com"
3. Attempt sign-in
4. Check request/response details for errors

## Advanced Debugging

### Check Apple Sign-In Service Status

```javascript
// Run in browser console to check Apple ID SDK
console.log('Apple ID SDK loaded:', typeof window.AppleID !== 'undefined');

// Check configuration
console.log('Client ID:', document.querySelector('meta[name="appleid-signin-client-id"]')?.content);
console.log('Redirect URI:', document.querySelector('meta[name="appleid-signin-redirect-uri"]')?.content);
```

### Verify Firebase Configuration

```javascript
// Check Firebase config in browser console
console.log('Firebase Config:', window.firebaseConfig);
```

## Production Deployment Checklist

- [x] Configuration files updated with correct domain
- [x] Domain association file accessible at `bloomg-flutter.web.app`
- [ ] Apple Developer Portal Service ID updated to `bloomg-flutter.web.app`
- [ ] Domain `bloomg-flutter.web.app` verified in Apple Developer Portal
- [ ] Firebase Apple provider enabled and configured
- [ ] Web app deployed to Firebase Hosting
- [ ] HTTPS enabled (required for Apple Sign-In)
- [ ] Apple Sign-In JavaScript SDK loading correctly
- [ ] Meta tags present in production HTML with correct domain

## Error Code Reference

| Error | Cause | Solution |
|-------|-------|----------|
| `UNKNOWN_SIWA_ERROR` | General configuration issue | Follow this troubleshooting guide |
| `invalid_client` | Service ID not found/configured | Verify Apple Developer Portal setup |
| `invalid_request` | Missing parameters | Check meta tags and implementation |
| `unauthorized_client` | Domain not verified | Complete domain verification process |

## Support Resources

- [Apple Sign-In Documentation](https://developer.apple.com/documentation/sign_in_with_apple)
- [Firebase Apple Sign-In Guide](https://firebase.google.com/docs/auth/web/apple)
- [Apple Developer Portal](https://developer.apple.com/account/)

## Next Steps

1. ✅ Update configuration files with correct domain (COMPLETED)
2. ⚠️ Update Apple Developer Portal Service ID configuration:
   - Change domain from `bloomg-flutter.firebaseapp.com` to `bloomg-flutter.web.app`
   - Update redirect URI to `https://bloomg-flutter.web.app/__/auth/handler`
   - Verify domain ownership
3. 🧪 Deploy and test:
   ```bash
   flutter build web
   firebase deploy --only hosting
   ```
4. 🔍 Test Apple Sign-In flow on deployed app
5. 📊 Monitor browser console for any remaining errors

**Critical**: The Apple Developer Portal configuration update is required before Apple Sign-In will work. The domain mismatch was the root cause of the 403 Forbidden and CORS errors.

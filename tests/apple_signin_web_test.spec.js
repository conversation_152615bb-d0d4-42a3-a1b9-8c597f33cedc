// Apple Sign-In Web Authentication Test
// This test validates Apple Sign-In functionality on the web platform

const { test, expect } = require('@playwright/test');

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000', // Flutter web dev server
  timeout: 30000,
  appleSignInServiceId: 'com.algomash.weblogin',
  firebaseProjectId: 'bloomg-flutter'
};

test.describe('Apple Sign-In Web Authentication', () => {

  test.beforeEach(async ({ page }) => {
    // Set longer timeout for authentication flows
    test.setTimeout(60000);

    // Navigate to the app
    await page.goto(TEST_CONFIG.baseUrl);

    // Wait for Flutter app to load
    await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 });
  });

  test('should display Apple Sign-In button on login page', async ({ page }) => {
    // Navigate to login page
    await page.click('[data-testid="login-button"]');

    // Wait for login page to load
    await page.waitForSelector('[data-testid="login-page"]');

    // Check if Apple Sign-In button is present
    const appleSignInButton = page.locator('[data-testid="apple-signin-button"]');
    await expect(appleSignInButton).toBeVisible();

    // Verify button text
    await expect(appleSignInButton).toContainText('Continue with Apple');

    // Verify button styling (should be black with white text)
    const buttonStyles = await appleSignInButton.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        backgroundColor: styles.backgroundColor,
        color: styles.color
      };
    });

    // Apple Sign-In buttons should be black
    expect(buttonStyles.backgroundColor).toContain('0, 0, 0'); // RGB for black
    expect(buttonStyles.color).toContain('255, 255, 255'); // RGB for white
  });

  test('should display Apple Sign-In button on signup page', async ({ page }) => {
    // Navigate to signup page
    await page.click('[data-testid="signup-button"]');

    // Wait for signup page to load
    await page.waitForSelector('[data-testid="signup-page"]');

    // Check if Apple Sign-In button is present
    const appleSignInButton = page.locator('[data-testid="apple-signin-button"]');
    await expect(appleSignInButton).toBeVisible();

    // Verify button text
    await expect(appleSignInButton).toContainText('Continue with Apple');
  });

  test('should initiate Apple Sign-In flow when button is clicked', async ({ page }) => {
    // Navigate to login page
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="login-page"]');

    // Set up network monitoring to catch Apple Sign-In requests
    const appleSignInRequests = [];
    page.on('request', request => {
      if (request.url().includes('appleid.apple.com') ||
        request.url().includes('apple.com/auth')) {
        appleSignInRequests.push(request);
      }
    });

    // Click Apple Sign-In button
    const appleSignInButton = page.locator('[data-testid="apple-signin-button"]');
    await appleSignInButton.click();

    // Wait for loading state
    await page.waitForSelector('[data-testid="loading-indicator"]', { timeout: 5000 });

    // Verify that Apple Sign-In flow was initiated
    // This might open a popup or redirect, so we check for network requests
    await page.waitForTimeout(3000); // Give time for requests to be made

    // Check if Apple Sign-In related requests were made
    expect(appleSignInRequests.length).toBeGreaterThan(0);
  });

  test('should handle Apple Sign-In cancellation gracefully', async ({ page }) => {
    // Navigate to login page
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="login-page"]');

    // Mock Apple Sign-In cancellation
    await page.route('**/appleid.apple.com/**', route => {
      route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'user_cancelled' })
      });
    });

    // Click Apple Sign-In button
    const appleSignInButton = page.locator('[data-testid="apple-signin-button"]');
    await appleSignInButton.click();

    // Wait for error handling
    await page.waitForTimeout(2000);

    // Verify user is still on login page (not redirected)
    await expect(page.locator('[data-testid="login-page"]')).toBeVisible();

    // Check if error message is displayed (optional)
    const errorMessage = page.locator('[data-testid="error-message"]');
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toContainText(/sign.*in.*cancelled|error/i);
    }
  });

  test('should validate Apple Sign-In service configuration', async ({ page }) => {
    // Check if the page has proper Apple Sign-In meta tags
    const appleSignInClientId = await page.locator('meta[name="appleid-signin-client-id"]').getAttribute('content');
    expect(appleSignInClientId).toBe(TEST_CONFIG.appleSignInServiceId);

    // Verify Firebase configuration is present
    const firebaseConfig = await page.evaluate(() => {
      return window.firebaseConfig || null;
    });

    if (firebaseConfig) {
      expect(firebaseConfig.projectId).toBe(TEST_CONFIG.firebaseProjectId);
    }
  });

  test('should handle network errors during Apple Sign-In', async ({ page }) => {
    // Navigate to login page
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="login-page"]');

    // Mock network error
    await page.route('**/appleid.apple.com/**', route => {
      route.abort('failed');
    });

    // Click Apple Sign-In button
    const appleSignInButton = page.locator('[data-testid="apple-signin-button"]');
    await appleSignInButton.click();

    // Wait for error handling
    await page.waitForTimeout(3000);

    // Verify error handling
    const errorMessage = page.locator('[data-testid="error-message"]');
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toContainText(/network.*error|connection.*failed/i);
    }

    // Verify user can retry
    await expect(appleSignInButton).toBeEnabled();
  });

  test('should maintain proper loading states during authentication', async ({ page }) => {
    // Navigate to login page
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="login-page"]');

    const appleSignInButton = page.locator('[data-testid="apple-signin-button"]');

    // Verify initial state
    await expect(appleSignInButton).toBeEnabled();
    await expect(appleSignInButton).not.toHaveClass(/loading/);

    // Mock slow Apple Sign-In response
    await page.route('**/appleid.apple.com/**', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ status: 'processing' })
      });
    });

    // Click Apple Sign-In button
    await appleSignInButton.click();

    // Verify loading state
    await expect(page.locator('[data-testid="loading-indicator"]')).toBeVisible();

    // Verify button is disabled during loading
    await expect(appleSignInButton).toBeDisabled();

    // Wait for loading to complete
    await page.waitForTimeout(3000);
  });

});

// Helper functions for test setup
test.describe('Apple Sign-In Configuration Validation', () => {

  test('should validate Apple Sign-In JavaScript SDK loading', async ({ page }) => {
    await page.goto(TEST_CONFIG.baseUrl);

    // Check if Apple Sign-In SDK is loaded
    const appleIdSdk = await page.evaluate(() => {
      return typeof window.AppleID !== 'undefined';
    });

    // Note: This might not be available in development mode
    // but should be present in production builds
    console.log('Apple ID SDK loaded:', appleIdSdk);
  });

  test('should validate cross-platform compatibility', async ({ page, browserName }) => {
    await page.goto(TEST_CONFIG.baseUrl);

    // Navigate to login page
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="login-page"]');

    // Apple Sign-In should be available on all browsers
    const appleSignInButton = page.locator('[data-testid="apple-signin-button"]');
    await expect(appleSignInButton).toBeVisible();

    console.log(`Apple Sign-In button visible on ${browserName}`);
  });

});

// Test data and utilities
const TEST_USERS = {
  validAppleUser: {
    email: '<EMAIL>',
    name: 'Test User'
  }
};

// Export test configuration for use in other test files
module.exports = {
  TEST_CONFIG,
  TEST_USERS
};

// Firebase Configuration for Web Platform
// This file contains Firebase configuration for Apple Sign-In web authentication

// Firebase configuration object
const firebaseConfig = {
  apiKey: "AIzaSyA3mIlC6O1_tGeH7OpuZmEcDYSDPcNcMqo",
  authDomain: "bloomg-flutter.firebaseapp.com",
  projectId: "bloomg-flutter",
  storageBucket: "bloomg-flutter.firebasestorage.app",
  messagingSenderId: "1068175978703",
  appId: "1:1068175978703:web:a62aa1bf9668a7b9dcc7ba",
  measurementId: "G-17QGQMB6GZ"
};

// Apple Sign-In configuration
const appleSignInConfig = {
  clientId: "com.algomash.weblogin",
  scope: "name email",
  redirectURI: "https://bloomg-flutter.web.app/__/auth/handler",
  state: "origin:web",
  usePopup: true
};

// Export configurations for use in Flutter web app
window.firebaseConfig = firebaseConfig;
window.appleSignInConfig = appleSignInConfig;

// Initialize Firebase (if needed for direct web usage)
if (typeof firebase !== 'undefined') {
  firebase.initializeApp(firebaseConfig);
}

console.log('Firebase and Apple Sign-In configurations loaded');

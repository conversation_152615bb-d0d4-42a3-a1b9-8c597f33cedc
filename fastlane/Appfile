# Appfile for Bloomg Flutter Apple Sign-In Configuration
# This file contains the configuration for Apple Developer Portal integration

# Apple Developer Portal Configuration
apple_id(ENV["APPLE_ID"] || "<EMAIL>")
team_id(ENV["APPLE_TEAM_ID"] || "598AQBZ36R")

# App Bundle Identifiers for different environments
app_identifier({
  "production" => "com.algomash.radiance",
  "staging" => "com.algomash.radiance.stg", 
  "development" => "com.algomash.radiance.dev"
})

# iTunes Connect Team ID (if different from Developer Portal)
itunes_connect_team_id(ENV["ITUNES_CONNECT_TEAM_ID"] || "598AQBZ36R")

# For App Store Connect API Key authentication
# Set these environment variables in your CI/CD system:
# - APP_STORE_CONNECT_API_KEY_ID
# - APP_STORE_CONNECT_API_ISSUER_ID  
# - APP_STORE_CONNECT_API_KEY_CONTENT (base64 encoded .p8 file content)

# Apple Sign-In Service Configuration
# Service ID used for web authentication
apple_signin_service_id = "com.algomash.weblogin"

# Firebase Configuration
firebase_project_id = "bloomg-flutter"
firebase_web_api_key = "AIzaSyA3mIlC6O1_tGeH7OpuZmEcDYSDPcNcMqo"
